from datetime import datetime
from sqlalchemy import Column, DateTime, Inte<PERSON>, Boolean
from app.core.database import Base


class TimestampMixin:
    created_at = Column(DateTime, default=datetime.now, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=True, comment="更新时间")


class SoftDeleteMixin:
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否已删除")
    deleted_at = Column(DateTime, nullable=True, comment="删除时间")


class BaseModel(Base, TimestampMixin, SoftDeleteMixin):
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
