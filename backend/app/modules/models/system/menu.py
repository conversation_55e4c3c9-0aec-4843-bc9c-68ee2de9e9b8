from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Text, Integer, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Menu(BaseModel):
    """菜单数据库模型"""

    __tablename__ = "system_menus"

    name = Column(String(100), nullable=False, comment="菜单名称")
    path = Column(String(255), nullable=True, comment="路由路径")
    component = Column(String(255), nullable=True, comment="组件路径")
    permission_id = Column(Integer, ForeignKey("system_permissions.id"), nullable=True, comment="关联权限ID")
    organization_id = Column(Integer, ForeignKey("system_organizations.id"), nullable=False, comment="所属机构ID")
    parent_id = Column(Integer, ForeignKey("system_menus.id"), nullable=True, comment="父菜单ID")
    target = Column(String(50), default="_self", comment="打开方式")
    is_hidden = Column(Boolean, default=False, comment="是否隐藏")
    sort_order = Column(Integer, default=0, comment="排序")
    icon = Column(String(100), nullable=True, comment="图标")
    keepalive = Column(Boolean, default=False, comment="是否缓存")
    redirect = Column(String(255), nullable=True, comment="重定向路径")
    meta = Column(JSON, nullable=True, comment="其他前端配置")
    
    # 关联关系
    permission = relationship("Permission", back_populates="menus")
    organization = relationship("Organization", back_populates="menus")
    parent = relationship("Menu", remote_side="Menu.id", back_populates="children")
    children = relationship("Menu", back_populates="parent", cascade="all, delete-orphan")
