from sqlalchemy import Boolean, Column, String, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Organization(BaseModel):
    """机构数据库模型"""

    __tablename__ = "system_organizations"

    code = Column(String(50), unique=True, index=True, nullable=False, comment="机构唯一标识")
    name = Column(String(100), nullable=False, comment="机构名称")
    description = Column(Text, nullable=True, comment="机构简介")
    status = Column(Boolean, default=True, comment="机构状态：True=启用，False=停用")
    
    # 关联关系
    roles = relationship("Role", back_populates="organization", cascade="all, delete-orphan")
    menus = relationship("Menu", back_populates="organization", cascade="all, delete-orphan")
    user_organizations = relationship("UserOrganization", back_populates="organization", cascade="all, delete-orphan")
