from sqlalchemy import Column, String, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Permission(BaseModel):
    """权限数据库模型"""

    __tablename__ = "system_permissions"

    name = Column(String(100), nullable=False, comment="权限名称")
    code = Column(String(100), unique=True, index=True, nullable=False, comment="权限码")
    module = Column(String(100), nullable=False, comment="所属模块")
    description = Column(Text, nullable=True, comment="权限描述")
    
    # 关联关系
    role_permissions = relationship("RolePermission", back_populates="permission", cascade="all, delete-orphan")
    menus = relationship("Menu", back_populates="permission")
