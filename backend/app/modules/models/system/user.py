from sqlalchemy import Boolean, Column, String, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class User(BaseModel):
    """用户数据库模型"""

    __tablename__ = "system_users"

    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    nickname = Column(String(100), nullable=True, comment="昵称")
    email = Column(String(255), unique=True, index=True, nullable=True, comment="邮箱")
    password = Column(String(255), nullable=False, comment="密码")
    avatar = Column(Text, nullable=True, comment="头像URL")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否为超级管理员")

    # 关联关系
    user_organizations = relationship("UserOrganization", back_populates="user", cascade="all, delete-orphan")
