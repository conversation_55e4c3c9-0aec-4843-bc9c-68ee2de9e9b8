from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Integer, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class UserOrganization(BaseModel):
    """用户机构关联数据库模型"""

    __tablename__ = "system_user_organizations"

    user_id = Column(Integer, ForeignKey("system_users.id"), nullable=False, comment="用户ID")
    organization_id = Column(Integer, ForeignKey("system_organizations.id"), nullable=False, comment="机构ID")
    role_id = Column(Integer, ForeignKey("system_roles.id"), nullable=True, comment="角色ID")
    is_active = Column(Boolean, default=True, comment="在该机构下是否激活")
    
    # 关联关系
    user = relationship("User", back_populates="user_organizations")
    organization = relationship("Organization", back_populates="user_organizations")
    role = relationship("Role", back_populates="user_organizations")
    
    # 添加唯一约束：同一用户在同一机构只能有一条记录
    __table_args__ = (
        UniqueConstraint("user_id", "organization_id", name="uk_user_organization"),
        {"comment": "用户机构关联表"},
    )
