from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.pagination import PageParams, PageResponse, get_page_params
from app.modules.system.user.deps import get_current_user
from app.modules.system.user.schemas import User
from app.modules.system.menu.schemas import (
    Menu,
    MenuCreate,
    MenuUpdate,
    MenuListParams,
    get_menu_list_params,
)
from app.modules.system.menu.service import MenuService

router = APIRouter()


@router.post("/", response_model=Menu, summary="创建菜单")
async def create_menu(
    menu_in: MenuCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    创建菜单接口

    只有超级管理员可以创建菜单
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以创建菜单",
        )
    return await MenuService.create(db, menu_in)


@router.get("/{menu_id}", response_model=Menu, summary="获取菜单详情")
async def get_menu(
    menu_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取菜单详情接口
    """
    menu = await MenuService.get_by_id(db, menu_id)
    if not menu:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="菜单不存在",
        )
    return menu


@router.put("/{menu_id}", response_model=Menu, summary="更新菜单")
async def update_menu(
    menu_id: int,
    menu_in: MenuUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    更新菜单接口

    只有超级管理员可以更新菜单
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以更新菜单",
        )
    return await MenuService.update(db, menu_id, menu_in)


@router.delete("/{menu_id}", summary="删除菜单")
async def delete_menu(
    menu_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    删除菜单接口

    只有超级管理员可以删除菜单
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以删除菜单",
        )
    success = await MenuService.delete(db, menu_id)
    return {"success": success}


@router.get("/", response_model=PageResponse[Menu], summary="获取菜单列表")
async def get_menu_list(
    db: AsyncSession = Depends(get_db),
    page_params: PageParams = Depends(get_page_params),
    query_params: MenuListParams = Depends(get_menu_list_params),
    current_user: User = Depends(get_current_user),
):
    """
    获取菜单列表接口

    支持分页和搜索功能：
    - page: 页码，从1开始
    - size: 每页数量，最大100
    - search: 搜索关键词，支持菜单标题和名称搜索
    - organization_id: 机构ID
    - permission_id: 权限ID
    - parent_id: 父级菜单ID

    超级管理员可以查看所有菜单
    机构管理员只能查看所属机构的菜单
    """
    # TODO: 添加机构权限过滤
    return await MenuService.get_list(db, page_params, query_params)


@router.get("/all", response_model=List[Menu], summary="获取所有菜单")
async def get_all_menus(
    organization_id: int = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取所有菜单接口

    超级管理员可以查看所有菜单
    机构管理员只能查看所属机构的菜单
    """
    # TODO: 添加机构权限过滤
    return await MenuService.get_all(db, organization_id)
