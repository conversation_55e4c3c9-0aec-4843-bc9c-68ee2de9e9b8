from typing import Optional, List

from fastapi import HTTPException, status
from sqlalchemy import or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.models.system.menu import Menu as DBMenu
from app.modules.system.menu.schemas import Menu, MenuCreate, MenuUpdate, MenuListParams
from app.core.pagination import PageParams, PageResponse, paginate_query


class MenuService:
    """菜单服务类"""

    @staticmethod
    async def get_by_id(db: AsyncSession, menu_id: int) -> Optional[Menu]:
        """
        根据ID获取菜单

        Args:
            db: 数据库会话
            menu_id: 菜单ID

        Returns:
            Optional[Menu]: 菜单对象，如果不存在则返回None
        """
        result = await db.execute(select(DBMenu).filter(DBMenu.id == menu_id))
        menu = result.scalars().first()
        if menu:
            return Menu.model_validate(menu)
        return None

    @staticmethod
    async def get_list(
        db: AsyncSession, page_params: PageParams, query_params: MenuListParams
    ) -> PageResponse[Menu]:
        """
        获取菜单列表

        Args:
            db: 数据库会话
            page_params: 分页参数
            query_params: 查询参数

        Returns:
            PageResponse[Menu]: 分页的菜单列表
        """
        query = select(DBMenu)

        # 机构过滤
        if query_params.organization_id:
            query = query.filter(DBMenu.organization_id == query_params.organization_id)

        # 权限过滤
        if query_params.permission_id:
            query = query.filter(DBMenu.permission_id == query_params.permission_id)

        # 父级菜单过滤
        if query_params.parent_id is not None:
            query = query.filter(DBMenu.parent_id == query_params.parent_id)

        # 搜索过滤
        if query_params.search:
            search_filter = or_(
                DBMenu.title.contains(query_params.search),
                DBMenu.name.contains(query_params.search),
            )
            query = query.filter(search_filter)

        # 排序
        query = query.order_by(DBMenu.sort.asc(), DBMenu.created_at.desc())

        return await paginate_query(db, query, page_params, Menu)

    @staticmethod
    async def get_all(db: AsyncSession, organization_id: Optional[int] = None) -> List[Menu]:
        """
        获取所有菜单

        Args:
            db: 数据库会话
            organization_id: 机构ID，如果提供则只获取该机构的菜单

        Returns:
            List[Menu]: 菜单列表
        """
        query = select(DBMenu)
        
        if organization_id:
            query = query.filter(DBMenu.organization_id == organization_id)
            
        query = query.order_by(DBMenu.sort.asc(), DBMenu.created_at.desc())
        result = await db.execute(query)
        menus = result.scalars().all()
        return [Menu.model_validate(menu) for menu in menus]

    @staticmethod
    async def create(db: AsyncSession, menu_in: MenuCreate) -> Menu:
        """
        创建菜单

        Args:
            db: 数据库会话
            menu_in: 菜单创建模型

        Returns:
            Menu: 创建的菜单对象
        """
        # 创建菜单
        db_menu = DBMenu(
            title=menu_in.title,
            name=menu_in.name,
            path=menu_in.path,
            component=menu_in.component,
            icon=menu_in.icon,
            sort=menu_in.sort,
            hidden=menu_in.hidden,
            keepalive=menu_in.keepalive,
            redirect=menu_in.redirect,
            meta=menu_in.meta,
            permission_id=menu_in.permission_id,
            organization_id=menu_in.organization_id,
            parent_id=menu_in.parent_id,
        )
        db.add(db_menu)
        await db.commit()
        await db.refresh(db_menu)

        return Menu.model_validate(db_menu)

    @staticmethod
    async def update(db: AsyncSession, menu_id: int, menu_in: MenuUpdate) -> Menu:
        """
        更新菜单

        Args:
            db: 数据库会话
            menu_id: 菜单ID
            menu_in: 菜单更新模型

        Returns:
            Menu: 更新后的菜单对象
        """
        result = await db.execute(select(DBMenu).filter(DBMenu.id == menu_id))
        db_menu = result.scalars().first()
        if not db_menu:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="菜单不存在",
            )

        # 更新字段
        update_data = menu_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_menu, field, value)

        await db.commit()
        await db.refresh(db_menu)

        return Menu.model_validate(db_menu)

    @staticmethod
    async def delete(db: AsyncSession, menu_id: int) -> bool:
        """
        删除菜单

        Args:
            db: 数据库会话
            menu_id: 菜单ID

        Returns:
            bool: 删除是否成功
        """
        result = await db.execute(select(DBMenu).filter(DBMenu.id == menu_id))
        db_menu = result.scalars().first()
        if not db_menu:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="菜单不存在",
            )

        # 删除菜单（级联删除子菜单）
        await db.delete(db_menu)
        await db.commit()

        return True
