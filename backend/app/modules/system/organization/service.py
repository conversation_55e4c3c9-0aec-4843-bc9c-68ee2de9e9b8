from typing import Optional

from fastapi import HTTPException, status
from sqlalchemy import func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.models.system.organization import Organization as DBOrganization
from app.modules.models.system.role import Role as DBRole
from app.modules.models.system.user_organization import UserOrganization as DBUserOrganization
from app.modules.system.organization.schemas import (
    Organization,
    OrganizationCreate,
    OrganizationUpdate,
    OrganizationListParams,
)
from app.core.pagination import PageParams, PageResponse, paginate_query


class OrganizationService:
    """机构服务类"""

    @staticmethod
    async def get_by_id(db: AsyncSession, organization_id: int) -> Optional[Organization]:
        """
        根据ID获取机构

        Args:
            db: 数据库会话
            organization_id: 机构ID

        Returns:
            Optional[Organization]: 机构对象，如果不存在则返回None
        """
        result = await db.execute(select(DBOrganization).filter(DBOrganization.id == organization_id))
        organization = result.scalars().first()
        if organization:
            return Organization.model_validate(organization)
        return None

    @staticmethod
    async def get_by_code(db: AsyncSession, code: str) -> Optional[Organization]:
        """
        根据机构标识获取机构

        Args:
            db: 数据库会话
            code: 机构标识

        Returns:
            Optional[Organization]: 机构对象，如果不存在则返回None
        """
        result = await db.execute(select(DBOrganization).filter(DBOrganization.code == code))
        organization = result.scalars().first()
        if organization:
            return Organization.model_validate(organization)
        return None

    @staticmethod
    async def get_list(
        db: AsyncSession, page_params: PageParams, query_params: OrganizationListParams
    ) -> PageResponse[Organization]:
        """
        获取机构列表

        Args:
            db: 数据库会话
            page_params: 分页参数
            query_params: 查询参数

        Returns:
            PageResponse[Organization]: 分页的机构列表
        """
        query = select(DBOrganization)

        # 搜索过滤
        if query_params.search:
            search_filter = or_(
                DBOrganization.name.contains(query_params.search),
                DBOrganization.code.contains(query_params.search),
            )
            query = query.filter(search_filter)

        # 排序
        query = query.order_by(DBOrganization.created_at.desc())

        return await paginate_query(db, query, page_params, Organization)

    @staticmethod
    async def create(db: AsyncSession, organization_in: OrganizationCreate) -> Organization:
        """
        创建机构

        Args:
            db: 数据库会话
            organization_in: 机构创建模型

        Returns:
            Organization: 创建的机构对象
        """
        # 检查机构标识是否已存在
        if await OrganizationService.get_by_code(db, organization_in.code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="机构标识已存在",
            )

        # 创建机构
        db_organization = DBOrganization(
            code=organization_in.code,
            name=organization_in.name,
            description=organization_in.description,
            status=organization_in.status,
        )
        db.add(db_organization)
        await db.commit()
        await db.refresh(db_organization)

        # 自动创建机构管理员角色
        admin_role = DBRole(
            name="机构管理员",
            description="机构管理员，拥有该机构的所有权限",
            organization_id=db_organization.id,
            is_admin=True,
        )
        db.add(admin_role)
        await db.commit()

        return Organization.model_validate(db_organization)

    @staticmethod
    async def update(db: AsyncSession, organization_id: int, organization_in: OrganizationUpdate) -> Organization:
        """
        更新机构

        Args:
            db: 数据库会话
            organization_id: 机构ID
            organization_in: 机构更新模型

        Returns:
            Organization: 更新后的机构对象
        """
        result = await db.execute(select(DBOrganization).filter(DBOrganization.id == organization_id))
        db_organization = result.scalars().first()
        if not db_organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="机构不存在",
            )

        # 更新字段
        update_data = organization_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_organization, field, value)

        await db.commit()
        await db.refresh(db_organization)

        return Organization.model_validate(db_organization)

    @staticmethod
    async def delete(db: AsyncSession, organization_id: int) -> bool:
        """
        删除机构

        Args:
            db: 数据库会话
            organization_id: 机构ID

        Returns:
            bool: 删除是否成功
        """
        result = await db.execute(select(DBOrganization).filter(DBOrganization.id == organization_id))
        db_organization = result.scalars().first()
        if not db_organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="机构不存在",
            )

        # 检查是否存在关联用户
        user_count_result = await db.execute(
            select(func.count(DBUserOrganization.id)).filter(DBUserOrganization.organization_id == organization_id)
        )
        user_count = user_count_result.scalar()
        if user_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="机构下存在用户，请先解除所有用户关联",
            )

        # 删除机构（级联删除角色和菜单）
        await db.delete(db_organization)
        await db.commit()

        return True
