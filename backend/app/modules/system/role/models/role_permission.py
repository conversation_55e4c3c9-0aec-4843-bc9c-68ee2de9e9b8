from sqlalchemy import Column, Integer, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class RolePermission(BaseModel):
    """角色权限关联数据库模型"""

    __tablename__ = "system_role_permissions"

    role_id = Column(Integer, ForeignKey("system_roles.id"), nullable=False, comment="角色ID")
    permission_id = Column(Integer, ForeignKey("system_permissions.id"), nullable=False, comment="权限ID")
    
    # 关联关系
    role = relationship("Role", back_populates="role_permissions")
    permission = relationship("Permission", back_populates="role_permissions")
    
    # 添加唯一约束：同一角色同一权限只能有一条记录
    __table_args__ = (
        UniqueConstraint("role_id", "permission_id", name="uk_role_permission"),
        {"comment": "角色权限关联表"},
    )
