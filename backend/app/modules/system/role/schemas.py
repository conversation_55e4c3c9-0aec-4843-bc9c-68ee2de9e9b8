from datetime import datetime
from typing import Optional, List, TYPE_CHECKING

from fastapi import Query
from pydantic import BaseModel, Field

if TYPE_CHECKING:
    from app.modules.system.permission.schemas import Permission


class RoleBase(BaseModel):
    """角色基础模型"""

    name: str = Field(..., description="角色名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="角色简介")


class RoleCreate(RoleBase):
    """角色创建模型"""

    organization_id: int = Field(..., description="所属机构ID")
    permission_ids: Optional[List[int]] = Field([], description="权限ID列表")


class RoleUpdate(BaseModel):
    """角色更新模型"""

    name: Optional[str] = Field(None, description="角色名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="角色简介")
    permission_ids: Optional[List[int]] = Field(None, description="权限ID列表")


class Role(RoleBase):
    """返回给API的角色模型"""

    id: int = Field(..., description="角色ID")
    organization_id: int = Field(..., description="所属机构ID")
    is_admin: bool = Field(..., description="是否为机构管理员")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class RoleWithPermissions(Role):
    """包含权限信息的角色模型"""

    permissions: List["Permission"] = Field([], description="角色权限列表")


class RoleListParams(BaseModel):
    """角色列表查询参数"""

    search: Optional[str] = Field(None, description="搜索关键词，支持角色名称搜索")
    organization_id: Optional[int] = Field(None, description="机构ID")


def get_role_list_params(
    search: Optional[str] = Query(None, description="搜索关键词，支持角色名称搜索"),
    organization_id: Optional[int] = Query(None, description="机构ID"),
) -> RoleListParams:
    """获取角色列表查询参数"""
    return RoleListParams(search=search, organization_id=organization_id)


# 解决前向引用问题
RoleWithPermissions.model_rebuild()
