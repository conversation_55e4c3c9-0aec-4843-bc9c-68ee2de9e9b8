import asyncio
import sys
from pathlib import Path

import typer
from utils.logging import get_logger

# 添加项目根目录到 Python 路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import init_models, AsyncSessionLocal
from app.models import *  # noqa: F401,F403
from app.modules.system.user.service import UserService
from app.modules.system.organization.service import OrganizationService
from app.modules.system.organization.schemas import OrganizationCreate

logger = get_logger(__name__)

# 创建 Typer 应用
app = typer.Typer(help="数据库管理工具")


@app.command()
def init_superuser(
    username: str = typer.Option("admin", help="管理员用户名"),
    email: str = typer.Option("<EMAIL>", help="管理员邮箱"),
    password: str = typer.Option(..., prompt=True, hide_input=True, help="管理员密码"),
):
    """
    创建管理员用户
    """
    logger.info(f"正在创建超级管理员用户: {username}...")
    asyncio.run(_init_superuser(username, email, password))
    logger.info("超级管理员用户创建完成")


@app.command()
def init_db():
    """
    初始化数据库
    """
    logger.info("正在初始化数据库...")
    asyncio.run(_init_db())
    logger.info("数据库初始化完成")


@app.command()
def init_data():
    """
    初始化基础数据
    """
    logger.info("正在初始化基础数据...")
    asyncio.run(_init_data())
    logger.info("基础数据初始化完成")


async def _init_db():
    """
    异步初始化数据库
    """
    await init_models()
    logger.info("数据库表创建完成")


async def _init_superuser(username: str, email: str, password: str):
    """
    异步创建管理员用户
    """
    async with AsyncSessionLocal() as session:
        user = await UserService.create_superuser(session, username, email, password)
        if user:
            logger.info(f"超级管理员用户 {username} 创建成功")
        else:
            logger.error(f"超级管理员用户 {username} 创建失败")


async def _init_data():
    """
    异步初始化基础数据
    """
    async with AsyncSessionLocal() as db:
        # 初始化默认机构
        logger.info("正在检查默认机构...")
        existing_org = await OrganizationService.get_by_code(db, "DEFAULT")
        if not existing_org:
            default_org = OrganizationCreate(
                code="DEFAULT", name="默认机构", description="系统默认机构，用于初始用户注册", status=True
            )
            organization = await OrganizationService.create(db, default_org)
            logger.info(f"成功创建默认机构: {organization.name}")
        else:
            logger.info("默认机构已存在，跳过创建")


if __name__ == "__main__":
    app()
