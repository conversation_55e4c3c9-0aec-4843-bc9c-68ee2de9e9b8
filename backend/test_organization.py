#!/usr/bin/env python3
"""
测试机构管理模块的基本功能
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.append(str(Path(__file__).parent))

from app.core.database import init_models, AsyncSessionLocal
from app.models import *  # noqa: F401,F403
from app.modules.system.organization.service import OrganizationService
from app.modules.system.organization.schemas import OrganizationCreate


async def test_organization_module():
    """测试机构管理模块"""
    print("开始测试机构管理模块...")
    
    # 初始化数据库
    print("1. 初始化数据库...")
    await init_models()
    print("   数据库初始化完成")
    
    async with AsyncSessionLocal() as db:
        # 测试创建机构
        print("2. 测试创建机构...")
        org_data = OrganizationCreate(
            code="TEST_ORG",
            name="测试机构",
            description="这是一个测试机构",
            status=True
        )
        
        try:
            organization = await OrganizationService.create(db, org_data)
            print(f"   机构创建成功: {organization.name} (ID: {organization.id})")
        except Exception as e:
            print(f"   机构创建失败: {e}")
            return
        
        # 测试获取机构
        print("3. 测试获取机构...")
        try:
            org_by_id = await OrganizationService.get_by_id(db, organization.id)
            org_by_code = await OrganizationService.get_by_code(db, "TEST_ORG")
            
            if org_by_id and org_by_code:
                print(f"   机构获取成功: {org_by_id.name}")
            else:
                print("   机构获取失败")
        except Exception as e:
            print(f"   机构获取失败: {e}")
        
        # 测试更新机构
        print("4. 测试更新机构...")
        try:
            from app.modules.system.organization.schemas import OrganizationUpdate
            update_data = OrganizationUpdate(
                name="更新后的测试机构",
                description="这是更新后的描述"
            )
            updated_org = await OrganizationService.update(db, organization.id, update_data)
            print(f"   机构更新成功: {updated_org.name}")
        except Exception as e:
            print(f"   机构更新失败: {e}")
        
        # 测试删除机构
        print("5. 测试删除机构...")
        try:
            success = await OrganizationService.delete(db, organization.id)
            if success:
                print("   机构删除成功")
            else:
                print("   机构删除失败")
        except Exception as e:
            print(f"   机构删除失败: {e}")
    
    print("机构管理模块测试完成!")


if __name__ == "__main__":
    asyncio.run(test_organization_module())
